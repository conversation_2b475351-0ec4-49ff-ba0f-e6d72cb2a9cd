<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, user-scalable=no">
    <meta http-equiv="Cross-Origin-Embedder-Policy" content="require-corp">
    <meta http-equiv="Cross-Origin-Opener-Policy" content="same-origin">
    <title>Cross-Origin Isolation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>Cross-Origin Isolation Test</h1>
    <p>This page tests whether Cross-Origin Isolation is properly configured for Godot web exports.</p>
    
    <div id="results"></div>
    
    <h2>Instructions</h2>
    <ol>
        <li>Start the local server: <code>python serve.py</code> or <code>node serve.js</code></li>
        <li>Access this page via: <code>http://localhost:8000/test_coi.html</code></li>
        <li>Check the test results above</li>
    </ol>
    
    <script>
        function addResult(message, type) {
            const resultsDiv = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            resultsDiv.appendChild(div);
        }
        
        function runTests() {
            // Test 1: Check if we're in a secure context
            if (window.isSecureContext) {
                addResult('✓ Secure context detected (HTTPS or localhost)', 'success');
            } else {
                addResult('✗ Not in secure context - SharedArrayBuffer may not work', 'error');
            }
            
            // Test 2: Check Cross-Origin Isolation
            if (window.crossOriginIsolated) {
                addResult('✓ Cross-Origin Isolation is enabled', 'success');
            } else {
                addResult('✗ Cross-Origin Isolation is NOT enabled', 'error');
            }
            
            // Test 3: Check SharedArrayBuffer availability
            if (typeof SharedArrayBuffer !== 'undefined') {
                addResult('✓ SharedArrayBuffer is available', 'success');
                
                // Test 4: Try to create a SharedArrayBuffer
                try {
                    const sab = new SharedArrayBuffer(1024);
                    addResult('✓ SharedArrayBuffer can be created successfully', 'success');
                } catch (e) {
                    addResult('✗ SharedArrayBuffer creation failed: ' + e.message, 'error');
                }
            } else {
                addResult('✗ SharedArrayBuffer is NOT available', 'error');
            }
            
            // Test 5: Check if we're served via file:// protocol
            if (window.location.protocol === 'file:') {
                addResult('✗ Served via file:// protocol - use a web server instead', 'error');
            } else {
                addResult('✓ Served via HTTP/HTTPS protocol', 'success');
            }
            
            // Test 6: Check headers (if possible)
            addResult('ℹ To verify headers, check browser DevTools → Network tab', 'info');
            
            // Summary
            const hasSharedArrayBuffer = typeof SharedArrayBuffer !== 'undefined';
            const hasCOI = window.crossOriginIsolated;
            
            if (hasSharedArrayBuffer && hasCOI) {
                addResult('🎉 All tests passed! Your Godot web export should work correctly.', 'success');
            } else {
                addResult('⚠️ Some tests failed. Your Godot web export may not work properly.', 'error');
            }
        }
        
        // Run tests when page loads
        window.addEventListener('load', runTests);
    </script>
</body>
</html>
