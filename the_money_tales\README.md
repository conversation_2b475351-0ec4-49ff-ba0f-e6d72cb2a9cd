# The Money Tales - Image Assets

This folder contains images generated by Freepik AI API for the story game.

## Image Naming Convention

Images should be named according to the following patterns:

### 1. By Node ID (Recommended)
- `root.png/jpg/jpeg/svg` - For the root scenario
- `choice1.png/jpg/jpeg/svg` - For choice1 scenario
- `choice2.png/jpg/jpeg/svg` - For choice2 scenario
- etc.

### 2. By Sanitized Title
- `the_crossroads.png/jpg/jpeg/svg` - For "The Crossroads" title
- `the_journey_continues.png/jpg/jpeg/svg` - For "The Journey Continues" title
- etc.

### 3. By Scenario Index
- `scenario_0.png/jpg/jpeg/svg` - For the first scenario (index 0)
- `scenario_1.png/jpg/jpeg/svg` - For the second scenario (index 1)
- etc.

### 4. Default Image
- `default.png/jpg/jpeg/svg` - Fallback image when specific scenario image is not found

## Supported Formats
- PNG (.png)
- JPEG (.jpg, .jpeg)
- SVG (.svg)

## How It Works

1. The game first tries to load an image based on the scenario's `node_id`
2. If not found, it tries using the sanitized title (lowercase, spaces replaced with underscores)
3. If still not found, it falls back to `scenario_X` where X is the scenario index
4. Finally, it uses the default image or project icon

## Adding New Images

1. Generate images using Freepik AI API
2. Save them in this folder with appropriate names
3. The game will automatically load them when displaying the corresponding scenarios

## Example File Structure
```
the_money_tales/
├── default.svg
├── root.svg
├── the_crossroads.svg
├── choice1.png
├── choice2.jpg
├── scenario_0.png
└── README.md
```
