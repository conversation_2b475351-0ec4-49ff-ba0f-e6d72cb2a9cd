[gd_scene load_steps=3 format=3 uid="uid://bqxvn8y7qr8ys"]

[ext_resource type="Script" path="res://QuizGame.gd" id="1_1x8qr"]

[sub_resource type="Theme" id="Theme_1"]

[node name="QuizGame" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_1x8qr")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0.2, 0.3, 0.5, 1)

[node name="MainContainer" type="HBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0

[node name="ImageContainer" type="Control" parent="MainContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_stretch_ratio = 2.0

[node name="StoryImage" type="TextureRect" parent="MainContainer/ImageContainer"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
expand_mode = 1
stretch_mode = 5

[node name="QuestionContainer" type="VBoxContainer" parent="MainContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_stretch_ratio = 1.0
theme = SubResource("Theme_1")

[node name="ScoreLabel" type="Label" parent="MainContainer/QuestionContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 18
text = "Score: 0 / 0"
horizontal_alignment = 1
vertical_alignment = 1

[node name="QuestionLabel" type="Label" parent="MainContainer/QuestionContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 24
text = "Question will appear here"
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 3

[node name="OptionsContainer" type="VBoxContainer" parent="MainContainer/QuestionContainer"]
layout_mode = 2
alignment = 1

[node name="NextButton" type="Button" parent="MainContainer/QuestionContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 20
text = "Next Question"
