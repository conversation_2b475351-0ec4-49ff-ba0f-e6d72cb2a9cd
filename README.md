# Quiz Game

A simple quiz game built with Godot Engine where players choose from multiple-choice options.

## Features

- **JSON-based Questions**: Load quiz questions from a JSON file
- **Multiple Choice**: Players select from 4 options per question
- **Visual Feedback**: Correct answers turn green, wrong answers turn red
- **Score Tracking**: Real-time score display throughout the quiz
- **Bottom-aligned UI**: Questions and options are positioned at the bottom of the screen
- **Background Image**: Placeholder background image (currently using Godot icon)
- **Restart Functionality**: Players can restart the quiz after completion

## How to Use

### Running the Game
1. Open the project in Godot Engine 4.2+
2. Press F5 or click the "Play" button to run the game
3. The quiz will start automatically with the first question

### Game Controls
- Click on any option button to select your answer
- After selecting, the correct answer will be highlighted in green
- If you selected incorrectly, your choice will be highlighted in red
- Click "Next Question" to proceed to the next question
- At the end, view your final score and click "Restart Quiz" to play again

### Customizing Quiz Content

#### JSON Format
Create or modify the `sample_quiz.json` file with your own questions:

```json
{
  "questions": [
    {
      "question": "Your question text here?",
      "options": ["Option 1", "Option 2", "Option 3", "Option 4"],
      "correct_answer": 0
    }
  ]
}
```

#### JSON Structure
- **question**: The question text (string)
- **options**: Array of 4 answer choices (strings)
- **correct_answer**: Index of the correct answer (0-3, where 0 is the first option)

#### Loading Custom Quiz Files
To use a different JSON file:
1. Place your JSON file in the project root
2. Modify line 20 in `QuizGame.gd`:
   ```gdscript
   load_quiz_data("res://your_quiz_file.json")
   ```

### Customizing Appearance

#### Background Image
Replace the background by:
1. Import your image into the Godot project
2. In the QuizGame.tscn scene, select the "Background" node
3. Change the "Texture" property to your image

#### UI Styling
- Modify button sizes by changing `custom_minimum_size` in the script
- Adjust colors by modifying the `Color` values in the `_on_option_selected` function
- Change text alignment and fonts through the Label node properties

## File Structure

- `QuizGame.tscn` - Main game scene with UI layout
- `QuizGame.gd` - Game logic and quiz management script
- `sample_quiz.json` - Sample quiz questions
- `project.godot` - Godot project configuration
- `icon.svg` - Placeholder background image

## Technical Details

- **Engine**: Godot 4.2+
- **Language**: GDScript
- **Rendering**: GL Compatibility mode for broader device support
- **UI**: Control nodes with VBoxContainer layout
- **Data**: JSON file parsing with error handling

## Extending the Game

You can easily extend this quiz game by:
- Adding timer functionality for each question
- Implementing difficulty levels
- Adding sound effects and music
- Creating categories or topics
- Adding images to questions
- Implementing a high score system
- Adding animations and transitions
