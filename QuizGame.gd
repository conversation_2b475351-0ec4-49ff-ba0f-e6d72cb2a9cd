extends Control

# Story Game Script
# Handles loading story data from API and managing story flow

@onready var background: ColorRect = $Background
@onready var main_container: HBoxContainer = $MainContainer
@onready var image_container: Control = $MainContainer/ImageContainer
@onready var story_image: TextureRect = $MainContainer/ImageContainer/StoryImage
@onready var question_container: VBoxContainer = $MainContainer/QuestionContainer
@onready var question_label: Label = $MainContainer/QuestionContainer/QuestionLabel
@onready var options_container: VBoxContainer = $MainContainer/QuestionContainer/OptionsContainer
@onready var score_label: Label = $MainContainer/QuestionContainer/ScoreLabel
@onready var next_button: Button = $MainContainer/QuestionContainer/NextButton

var story_data: Dictionary = {}
var current_story_index: int = 0
var story_path: Array = [] # Track the path taken through the story
var selected_choice: int = -1
var current_level: int = 0 # 0=root, 1=level2, 2=level3
var story_tree: Dictionary = {}
var book_info: Dictionary = {}
var images_folder: String = "res://the_money_tales/"
var default_image_path: String = "res://icon.svg"

func _ready():
	print("Story Game starting...")

	# Try to load story data from API, fallback to local JSON
	load_story_data_from_api()

func load_story_data_from_api():
	"""Load story data from localStorage or fallback to local JSON"""
	# Try to get data from localStorage first (set by the web page)
	var js_code = """
	(function() {
		var data = localStorage.getItem('storyGameData');
		if (data) {
			return data;
		}
		return null;
	})();
	"""

	# Execute JavaScript to get localStorage data
	var localStorage_data = null
	if OS.has_feature("web"):
		localStorage_data = JavaScriptBridge.eval(js_code)

	if localStorage_data and localStorage_data != "null":
		# Parse the localStorage data
		var json = JSON.new()
		var parse_result = json.parse(localStorage_data)

		if parse_result == OK:
			story_data = json.data

			# Extract story tree and book info if available
			if story_data.has("story_tree"):
				story_tree = story_data.story_tree
			if story_data.has("book_info"):
				book_info = story_data.book_info

			setup_ui()
			display_current_story()
			return

	# Fallback to local JSON file
	load_story_data("res://sample_quiz.json")

func load_story_data(file_path: String):
	"""Load story data from JSON file"""
	var file = FileAccess.open(file_path, FileAccess.READ)
	if file == null:
		print("Error: Could not open story file: ", file_path)
		# Create default story data if file doesn't exist
		create_default_story_data()
		return

	var json_string = file.get_as_text()
	file.close()

	var json = JSON.new()
	var parse_result = json.parse(json_string)

	if parse_result != OK:
		print("Error parsing JSON: ", json.get_error_message())
		create_default_story_data()
		return

	story_data = json.data

	# Extract story tree and book info if available
	if story_data.has("story_tree"):
		story_tree = story_data.story_tree
	if story_data.has("book_info"):
		book_info = story_data.book_info

	setup_ui()
	display_current_story()

func create_default_story_data():
	"""Create default story data if JSON file is not found"""
	story_data = {
		"book_info": {
			"title": "Sample Story",
			"author": "Unknown Author",
			"changeLocation": "Chapter 1",
			"whatIfPrompt": "What if the story took a different turn?"
		},
		"questions": [
			{
				"question": "You find yourself at a crossroads in the story. The path ahead splits into three directions, each leading to a different fate.",
				"title": "The Crossroads",
				"options": ["Take the left path through the dark forest", "Take the middle path across the bridge", "Take the right path up the mountain"],
				"correct_answer": -1,
				"choice_ids": ["choice1", "choice2", "choice3"],
				"level": 0,
				"node_id": "root"
			},
			{
				"question": "The adventure continues as you face new challenges and discoveries along your chosen path.",
				"title": "The Journey Continues",
				"options": ["Continue forward", "Turn back"],
				"correct_answer": -1,
				"choice_ids": ["choice1_1", "choice1_2"],
				"level": 1,
				"node_id": "choice1",
				"is_ending": false
			},
			{
				"question": "Your journey reaches its conclusion. The choices you made have led you to this final moment in the story.",
				"title": "The End",
				"options": [],
				"correct_answer": -1,
				"choice_ids": [],
				"level": 2,
				"node_id": "choice1_1",
				"is_ending": true
			}
		]
	}

	setup_ui()
	display_current_story()

func setup_ui():
	"""Setup the user interface"""
	# Connect next button
	next_button.pressed.connect(_on_next_button_pressed)
	next_button.visible = false

	# Load default image
	load_story_image("default")

	# Update story progress display
	update_story_display()

func load_story_image(image_name: String):
	"""Load an image for the current story scenario"""
	var extensions = [".png", ".jpg", ".jpeg", ".svg"]

	# Try each extension
	for ext in extensions:
		var image_path = images_folder + image_name + ext
		if FileAccess.file_exists(image_path):
			var texture = load(image_path)
			if texture:
				story_image.texture = texture
				print("Loaded image: ", image_path)
				return

	# Fallback to default image
	for ext in extensions:
		var default_path = images_folder + "default" + ext
		if FileAccess.file_exists(default_path):
			var texture = load(default_path)
			if texture:
				story_image.texture = texture
				print("Loaded default image: ", default_path)
				return

	# Final fallback to project icon
	var default_texture = load(default_image_path)
	if default_texture:
		story_image.texture = default_texture
		print("Loaded fallback image: ", default_image_path)

func display_current_story():
	"""Display the current story scenario and its options"""
	if current_story_index >= story_data.questions.size():
		show_story_ending()
		return

	var story_scenario = story_data.questions[current_story_index]

	# Load image for current scenario
	var image_name = "scenario_" + str(current_story_index)
	if story_scenario.has("node_id"):
		image_name = story_scenario.node_id
	elif story_scenario.has("title"):
		# Use title as image name (sanitized)
		image_name = story_scenario.title.to_lower().replace(" ", "_").replace(",", "").replace(".", "")

	load_story_image(image_name)

	# Display story title and text
	if story_scenario.has("title"):
		question_label.text = story_scenario.title + "\n\n" + story_scenario.question
	else:
		question_label.text = story_scenario.question

	# Clear existing option buttons
	for child in options_container.get_children():
		if child is Button:
			child.queue_free()

	# Create choice buttons (only if there are options)
	if story_scenario.options.size() > 0:
		for i in range(story_scenario.options.size()):
			var choice_button = Button.new()
			choice_button.text = story_scenario.options[i]
			choice_button.custom_minimum_size = Vector2(300, 80)
			# Set larger font size for choice buttons
			choice_button.add_theme_font_size_override("font_size", 18)
			choice_button.pressed.connect(_on_choice_selected.bind(i))
			options_container.add_child(choice_button)

		# Reset selection state
		selected_choice = -1
		next_button.visible = false
	else:
		# This is an ending scenario
		show_story_ending()

func display_current_question():
	"""Legacy function - redirects to display_current_story"""
	display_current_story()

func _on_choice_selected(choice_index: int):
	"""Handle story choice selection"""
	selected_choice = choice_index
	var story_scenario = story_data.questions[current_story_index]

	# Disable all choice buttons
	for i in range(options_container.get_child_count()):
		var button = options_container.get_child(i)
		if button is Button:
			button.disabled = true
			# Highlight the selected choice
			if i == selected_choice:
				button.modulate = Color.CYAN

	# Add the choice to the story path
	if story_scenario.has("choice_ids") and choice_index < story_scenario.choice_ids.size():
		story_path.append(story_scenario.choice_ids[choice_index])

	# Show next button
	next_button.visible = true

func _on_option_selected(option_index: int):
	"""Legacy function - redirects to _on_choice_selected"""
	_on_choice_selected(option_index)

func _on_next_button_pressed():
	"""Move to next story scenario"""
	# Find the next scenario based on the choice made
	var current_scenario = story_data.questions[current_story_index]

	if selected_choice >= 0 and current_scenario.has("choice_ids"):
		var chosen_id = current_scenario.choice_ids[selected_choice]

		# Find the next scenario with matching node_id
		for i in range(story_data.questions.size()):
			var scenario = story_data.questions[i]
			if scenario.has("node_id") and scenario.node_id == chosen_id:
				current_story_index = i
				break
	else:
		# No choice made or no choices available, move to next in sequence
		current_story_index += 1

	# Reset button colors
	for child in options_container.get_children():
		if child is Button:
			child.modulate = Color.WHITE
			child.disabled = false

	display_current_story()

func update_story_display():
	"""Update the story progress display"""
	if book_info.has("title"):
		score_label.text = "Story: %s\nProgress: %d / %d" % [book_info.title, current_story_index + 1, story_data.questions.size()]
	else:
		score_label.text = "Progress: %d / %d" % [current_story_index + 1, story_data.questions.size()]

func update_score_display():
	"""Legacy function - redirects to update_story_display"""
	update_story_display()

func show_story_ending():
	"""Show story ending"""
	var current_scenario = story_data.questions[current_story_index]

	if current_scenario.has("is_ending") and current_scenario.is_ending:
		question_label.text = "Story Complete!\n\n" + current_scenario.question
	else:
		question_label.text = "Story Complete!\n\nYou have reached the end of this story path. Thank you for playing!"

	# Clear option buttons
	for child in options_container.get_children():
		if child is Button:
			child.queue_free()

	# Show story completion message
	var completion_label = Label.new()
	completion_label.text = "Your choices have shaped this unique story path."
	completion_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	completion_label.add_theme_font_size_override("font_size", 20)
	options_container.add_child(completion_label)

	# Add restart button
	var restart_button = Button.new()
	restart_button.text = "Restart Story"
	restart_button.custom_minimum_size = Vector2(200, 60)
	restart_button.add_theme_font_size_override("font_size", 18)
	restart_button.pressed.connect(_on_restart_story)
	options_container.add_child(restart_button)

	next_button.visible = false

func show_final_score():
	"""Legacy function - redirects to show_story_ending"""
	show_story_ending()

func _on_restart_story():
	"""Restart the story"""
	current_story_index = 0
	story_path.clear()
	selected_choice = -1
	current_level = 0
	update_story_display()
	display_current_story()

func _on_restart_quiz():
	"""Legacy function - redirects to _on_restart_story"""
	_on_restart_story()

