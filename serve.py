#!/usr/bin/env python3
"""
Simple HTTP server with Cross-Origin Isolation headers for Godot web exports.
This server adds the necessary headers to enable SharedArrayBuffer and threading.

Usage:
    python serve.py [port]
    
Default port is 8000.
Access your game at: http://localhost:8000/whatif_template.html
"""

import http.server
import socketserver
import sys
import os
from urllib.parse import urlparse

class COIHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """HTTP request handler that adds Cross-Origin Isolation headers."""
    
    def end_headers(self):
        # Add Cross-Origin Isolation headers
        self.send_header('Cross-Origin-Embedder-Policy', 'require-corp')
        self.send_header('Cross-Origin-Opener-Policy', 'same-origin')
        
        # Add CORP header for resources
        path = urlparse(self.path).path
        if path.endswith(('.js', '.wasm', '.pck', '.html', '.css', '.png', '.jpg', '.svg')):
            self.send_header('Cross-Origin-Resource-Policy', 'cross-origin')
        
        # Add cache control for development
        if path.endswith(('.js', '.wasm', '.pck')):
            self.send_header('Cache-Control', 'no-cache')
        
        super().end_headers()
    
    def log_message(self, format, *args):
        """Override to provide cleaner logging."""
        print(f"[{self.address_string()}] {format % args}")

def main():
    # Get port from command line argument or use default
    port = 8000
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print(f"Invalid port number: {sys.argv[1]}")
            sys.exit(1)
    
    # Change to the directory containing this script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # Create and start the server
    with socketserver.TCPServer(("", port), COIHTTPRequestHandler) as httpd:
        print(f"Starting server with Cross-Origin Isolation support...")
        print(f"Serving directory: {os.getcwd()}")
        print(f"Server running at: http://localhost:{port}/")
        print(f"Access your game at: http://localhost:{port}/whatif_template.html")
        print(f"Press Ctrl+C to stop the server")
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nServer stopped.")

if __name__ == "__main__":
    main()
